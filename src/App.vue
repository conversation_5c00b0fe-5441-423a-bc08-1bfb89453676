<script setup lang="ts">
import HuaweiMap from './components/HuaweiMap.vue'
import MapConfigPanel from './components/MapConfigPanel.vue'
import MapEventMonitor from './components/MapEventMonitor.vue'
import MarkerPanel from './components/MarkerPanel.vue'
import InfoWindowPanel from './components/InfoWindowPanel.vue'
import { ref } from 'vue'
import { useMapConfig, type MapConfigOptions } from './composables/useMapConfig'

// 使用地图配置组合式函数
const { config, updateConfig } = useMapConfig({
  center: { lat: 23.130415047434678, lng: 113.32380294799805 },
  zoom: 10,
  controls: {
    zoom: true,
    scale: true,
    location: true
  }
})

// 地图实例引用
const mapRef = ref<InstanceType<typeof HuaweiMap>>()

// 事件监控状态
const enableEventMonitor = ref(true)

// 事件处理
const onMapReady = (map: any) => {
  console.log('地图加载完成:', map)
}

const onMapClick = (event: any) => {
  console.log('地图点击事件:', event)
}

const onMapError = (error: string) => {
  console.error('地图错误:', error)
}

// 配置变化处理
const onConfigChange = (newConfig: MapConfigOptions) => {
  updateConfig(newConfig)
  console.log('配置已更新:', newConfig)
}

// 标记事件处理
const onMarkerClick = (marker: any, event: any) => {
  console.log('标记点击:', marker, event)
}

const onAddMarker = (markerData: any) => {
  mapRef.value?.addMarker(markerData)
}

const onRemoveMarker = (id: string) => {
  mapRef.value?.removeMarker(id)
}

const onUpdateMarker = (id: string, updates: any) => {
  mapRef.value?.updateMarker(id, updates)
}

const onSelectMarker = (id: string) => {
  mapRef.value?.selectMarker(id)
}

const onToggleMarkerVisibility = (id: string) => {
  mapRef.value?.toggleMarkerVisibility(id)
}

const onClearMarkers = () => {
  mapRef.value?.clearMarkers()
}

const onFitMarkersToView = () => {
  mapRef.value?.fitMarkersToView()
}

// 信息窗事件处理
const onAddInfoWindow = (infoWindowData: any) => {
  mapRef.value?.addInfoWindow(infoWindowData)
}

const onRemoveInfoWindow = (id: string) => {
  mapRef.value?.removeInfoWindow(id)
}

const onUpdateInfoWindow = (id: string, updates: any) => {
  mapRef.value?.updateInfoWindow(id, updates)
}

const onOpenInfoWindow = (id: string) => {
  mapRef.value?.openInfoWindow(id)
}

const onCloseInfoWindow = (id: string) => {
  mapRef.value?.closeInfoWindow(id)
}

const onCloseAllInfoWindows = () => {
  mapRef.value?.closeAllInfoWindows()
}

const onClearInfoWindows = () => {
  mapRef.value?.clearInfoWindows()
}

const onToggleInfoWindow = (id: string) => {
  mapRef.value?.toggleInfoWindow(id)
}
</script>

<template>
  <div class="app">
    <header class="app-header">
      <h1>华为地图 Vue3 组件示例</h1>
      <p>基于华为地图API的Vue3地图组件</p>
    </header>

    <main class="app-main">
      <div class="map-section">
        <h2>华为地图组件</h2>
        <HuaweiMap
          ref="mapRef"
          :center="config.center"
          :zoom="config.zoom"
          :language="config.language"
          :source-type="config.sourceType"
          :map-type="config.mapType"
          :min-zoom="config.minZoom"
          :max-zoom="config.maxZoom"
          :copyright-control="config.controls?.copyright"
          :location-control="config.controls?.location"
          :navigation-control="config.controls?.navigation"
          :rotate-control="config.controls?.rotate"
          :scale-control="config.controls?.scale"
          :zoom-control="config.controls?.zoom"
          :zoom-slider="config.controls?.zoomSlider"
          :logo-position="config.style?.logoPosition"
          :preset-style-id="config.style?.presetStyleId"
          :opacity="config.style?.opacity"
          :enable-event-monitor="enableEventMonitor"
          width="100%"
          height="500px"
          @map-ready="onMapReady"
          @map-click="onMapClick"
          @map-error="onMapError"
          @marker-click="onMarkerClick"
        />
      </div>

      <div class="sidebar">
        <div class="config-section">
          <h2>地图配置</h2>
          <MapConfigPanel
            v-model="config"
            @config-change="onConfigChange"
          />
        </div>

        <div class="marker-section">
          <MarkerPanel
            :markers="mapRef?.markers || []"
            :selected-marker="mapRef?.selectedMarker || null"
            :marker-count="mapRef?.markerCount || 0"
            :visible-marker-count="mapRef?.visibleMarkerCount || 0"
            :map-center="config.center"
            @add-marker="onAddMarker"
            @remove-marker="onRemoveMarker"
            @update-marker="onUpdateMarker"
            @select-marker="onSelectMarker"
            @toggle-visibility="onToggleMarkerVisibility"
            @clear-markers="onClearMarkers"
            @fit-to-view="onFitMarkersToView"
          />
        </div>

        <div class="infowindow-section">
          <InfoWindowPanel
            :info-windows="mapRef?.infoWindows || []"
            :active-info-window="mapRef?.activeInfoWindow || null"
            :info-window-count="mapRef?.infoWindowCount || 0"
            :visible-info-window-count="mapRef?.visibleInfoWindowCount || 0"
            :markers="mapRef?.markers || []"
            :map-center="config.center"
            @add-infowindow="onAddInfoWindow"
            @remove-infowindow="onRemoveInfoWindow"
            @update-infowindow="onUpdateInfoWindow"
            @open-infowindow="onOpenInfoWindow"
            @close-infowindow="onCloseInfoWindow"
            @close-all-infowindows="onCloseAllInfoWindows"
            @clear-infowindows="onClearInfoWindows"
            @toggle-infowindow="onToggleInfoWindow"
          />
        </div>

        <div class="event-section" v-if="enableEventMonitor">
          <MapEventMonitor
            :event-history="mapRef?.eventHistory || []"
            :current-center="mapRef?.currentCenter"
            :current-zoom="mapRef?.currentZoom"
            :current-heading="mapRef?.currentHeading"
            @clear-events="() => mapRef?.clearEventHistory?.()"
          />
        </div>
      </div>
    </main>
  </div>
</template>

<style scoped>
.app {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.app-header {
  text-align: center;
  margin-bottom: 40px;
}

.app-header h1 {
  color: #2c3e50;
  margin-bottom: 10px;
}

.app-header p {
  color: #7f8c8d;
  font-size: 16px;
}

.app-main {
  display: grid;
  grid-template-columns: 1fr 450px;
  gap: 30px;
  height: calc(100vh - 200px);
}

@media (max-width: 1400px) {
  .app-main {
    grid-template-columns: 1fr;
    height: auto;
  }
}

.map-section {
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  min-height: 0;
}

.map-section h2 {
  margin-top: 0;
  margin-bottom: 20px;
  color: #2c3e50;
}

.sidebar {
  display: flex;
  flex-direction: column;
  gap: 20px;
  min-height: 0;
}

.config-section {
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.config-section h2 {
  margin-top: 0;
  margin-bottom: 20px;
  color: #2c3e50;
}

.marker-section {
  flex: 1;
  min-height: 0;
}

.infowindow-section {
  flex: 1;
  min-height: 0;
}

.event-section {
  flex: 1;
  min-height: 0;
}
</style>
