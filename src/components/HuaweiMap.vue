<template>
  <div
    ref="mapContainer"
    class="huawei-map-container"
    :style="{ width: width, height: height }"
  >
    <div v-if="loading" class="map-loading">
      <div class="loading-spinner"></div>
      <p>地图加载中...</p>
    </div>
    <div v-if="error" class="map-error">
      <p>{{ error }}</p>
      <button @click="retryLoad" class="retry-button">重试</button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, watch, nextTick } from 'vue';
import { loadHuaweiMapAPI, isHuaweiMapAPILoaded } from '../utils/map-loader';
import { defaultMapConfig } from '../config/map-config';
import { useMapEvents, type MapEventHandlers } from '../composables/useMapEvents';

// Props定义
interface Props {
  // 地图容器尺寸
  width?: string;
  height?: string;

  // 地图基础配置
  center?: { lat: number; lng: number };
  zoom?: number;
  language?: string;
  sourceType?: 'vector' | 'raster';
  mapType?: 'ROADMAP' | 'TERRAIN';

  // 缩放级别限制
  minZoom?: number;
  maxZoom?: number;

  // 控件显示配置
  copyrightControl?: boolean;
  locationControl?: boolean;
  navigationControl?: boolean;
  rotateControl?: boolean;
  scaleControl?: boolean;
  zoomControl?: boolean;
  zoomSlider?: boolean;

  // 样式配置
  logoPosition?: 'BOTTOM_LEFT' | 'BOTTOM_RIGHT' | 'TOP_LEFT' | 'TOP_RIGHT';
  presetStyleId?: 'standard' | 'night' | 'simple';
  opacity?: number;

  // API配置
  apiKey?: string;

  // 事件配置
  enableEventMonitor?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  width: '100%',
  height: '400px',
  center: () => defaultMapConfig.defaultCenter,
  zoom: defaultMapConfig.defaultZoom,
  language: defaultMapConfig.defaultLanguage,
  sourceType: defaultMapConfig.defaultSourceType,
  mapType: 'ROADMAP',
  minZoom: 2,
  maxZoom: 20,
  copyrightControl: false,
  locationControl: false,
  navigationControl: false,
  rotateControl: false,
  scaleControl: false,
  zoomControl: true,
  zoomSlider: false,
  logoPosition: 'BOTTOM_LEFT',
  presetStyleId: 'standard',
  opacity: 1,
  enableEventMonitor: false
});

// Emits定义
interface Emits {
  (e: 'map-ready', map: any): void;
  (e: 'map-click', event: any): void;
  (e: 'map-dblclick', event: any): void;
  (e: 'map-rightclick', event: any): void;
  (e: 'map-mousemove', event: any): void;
  (e: 'map-mousedown', event: any): void;
  (e: 'map-mouseup', event: any): void;
  (e: 'map-drag', event: any): void;
  (e: 'move-start'): void;
  (e: 'move-end'): void;
  (e: 'center-changed', center: { lat: number; lng: number }): void;
  (e: 'zoom-changed', zoom: number): void;
  (e: 'heading-changed', heading: number): void;
  (e: 'map-error', error: string): void;
}

const emit = defineEmits<Emits>();

// 响应式数据
const mapContainer = ref<HTMLElement>();
const loading = ref(true);
const error = ref<string>('');
const map = ref<any>();

// 事件处理器配置
const eventHandlers: MapEventHandlers = {
  onClick: (event) => emit('map-click', event),
  onDoubleClick: (event) => emit('map-dblclick', event),
  onRightClick: (event) => emit('map-rightclick', event),
  onMouseMove: (event) => emit('map-mousemove', event),
  onMouseDown: (event) => emit('map-mousedown', event),
  onMouseUp: (event) => emit('map-mouseup', event),
  onDrag: (event) => emit('map-drag', event),
  onMoveStart: () => emit('move-start'),
  onMoveEnd: () => emit('move-end'),
  onCenterChanged: (center) => emit('center-changed', center),
  onZoomChanged: (zoom) => emit('zoom-changed', zoom),
  onHeadingChanged: (heading) => emit('heading-changed', heading)
};

// 使用事件管理组合式函数
const {
  eventHistory,
  currentCenter,
  currentZoom,
  currentHeading,
  bindMapEvents,
  unbindAllEvents,
  clearHistory
} = useMapEvents(map, props.enableEventMonitor ? eventHandlers : undefined);

// 初始化地图
const initMap = async () => {
  try {
    loading.value = true;
    error.value = '';

    // 确保容器存在
    if (!mapContainer.value) {
      throw new Error('地图容器未找到');
    }

    // 加载华为地图API
    await loadHuaweiMapAPI(props.apiKey);

    // 等待DOM更新
    await nextTick();

    // 创建地图配置
    const mapOptions: any = {
      center: props.center,
      zoom: props.zoom,
      language: props.language,
      sourceType: props.sourceType,
      mapType: props.mapType,
      minZoom: props.minZoom,
      maxZoom: props.maxZoom,
      copyrightControl: props.copyrightControl,
      locationControl: props.locationControl,
      navigationControl: props.navigationControl,
      rotateControl: props.rotateControl,
      scaleControl: props.scaleControl,
      zoomControl: props.zoomControl,
      zoomSlider: props.zoomSlider,
      logoPosition: props.logoPosition,
      presetStyleId: props.presetStyleId
    };

    // 创建地图实例
    map.value = new window.HWMapJsSDK.HWMap(mapContainer.value, mapOptions);

    // 设置透明度
    if (props.opacity !== 1) {
      map.value.setOpacity(props.opacity);
    }

    // 绑定事件（如果启用了事件监控）
    if (props.enableEventMonitor) {
      bindMapEvents();
    } else {
      // 绑定基础事件
      bindBasicEvents();
    }

    loading.value = false;
    emit('map-ready', map.value);

  } catch (err) {
    loading.value = false;
    const errorMessage = err instanceof Error ? err.message : '地图初始化失败';
    error.value = errorMessage;
    emit('map-error', errorMessage);
    console.error('华为地图初始化失败:', err);
  }
};

// 绑定基础地图事件（不使用事件监控时）
const bindBasicEvents = () => {
  if (!map.value) return;

  // 点击事件
  map.value.on('click', (event: any) => {
    emit('map-click', event);
  });

  // 双击事件
  map.value.on('dblclick', (event: any) => {
    emit('map-dblclick', event);
  });

  // 右键点击事件
  map.value.on('contextmenu', (event: any) => {
    emit('map-rightclick', event);
  });

  // 中心点变化事件
  map.value.onCenterChanged(() => {
    const center = map.value.getCenter();
    emit('center-changed', center);
  });

  // 缩放级别变化事件
  map.value.onZoomChanged(() => {
    const zoom = map.value.getZoom();
    emit('zoom-changed', zoom);
  });
};

// 重试加载
const retryLoad = () => {
  initMap();
};

// 监听props变化
watch(() => props.center, (newCenter) => {
  if (map.value && newCenter) {
    map.value.setCenter(newCenter);
  }
});

watch(() => props.zoom, (newZoom) => {
  if (map.value && newZoom) {
    map.value.setZoom(newZoom);
  }
});

watch(() => props.opacity, (newOpacity) => {
  if (map.value && newOpacity !== undefined) {
    map.value.setOpacity(newOpacity);
  }
});

// 生命周期
onMounted(() => {
  initMap();
});

onUnmounted(() => {
  // 清理地图实例
  if (map.value) {
    // 华为地图没有明确的销毁方法，这里只是清空引用
    map.value = undefined;
  }
});

// 暴露地图实例和事件数据给父组件
defineExpose({
  map: map,
  getMap: () => map.value,
  reload: initMap,
  // 事件相关
  eventHistory,
  currentCenter,
  currentZoom,
  currentHeading,
  clearEventHistory: clearHistory
});
</script>

<style scoped>
.huawei-map-container {
  position: relative;
  background-color: #f0f0f0;
}

.map-loading {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  background-color: rgba(255, 255, 255, 0.9);
  z-index: 1000;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #3498db;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.map-error {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  background-color: rgba(255, 255, 255, 0.9);
  z-index: 1000;
  color: #e74c3c;
}

.retry-button {
  margin-top: 16px;
  padding: 8px 16px;
  background-color: #3498db;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.3s;
}

.retry-button:hover {
  background-color: #2980b9;
}
</style>
