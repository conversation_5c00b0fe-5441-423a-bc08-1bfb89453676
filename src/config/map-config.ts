// 华为地图配置文件
import { readFileSync } from 'fs';
import { resolve } from 'path';

// 华为地图API配置
export interface HuaweiMapConfig {
  apiKey: string;
  apiUrl: string;
  defaultCenter: { lat: number; lng: number };
  defaultZoom: number;
  defaultLanguage: string;
  defaultSourceType: 'vector' | 'raster';
}

// 读取API密钥
function getApiKey(): string {
  try {
    // 在浏览器环境中，我们需要通过其他方式获取API密钥
    // 这里先返回一个占位符，实际使用时需要从环境变量或配置中获取
    return 'DQEDAD1UhYOqf6mN2VJ1cy2A6BzTN7gSr28xX0KO4hUw1cDESelU6d5WeWn3mezhd3rxLtUSsDJpltkcZLYCoLxcNkpJsYkefIg1Pg==';
  } catch (error) {
    console.warn('无法读取API密钥文件，使用默认密钥');
    return 'DQEDAD1UhYOqf6mN2VJ1cy2A6BzTN7gSr28xX0KO4hUw1cDESelU6d5WeWn3mezhd3rxLtUSsDJpltkcZLYCoLxcNkpJsYkefIg1Pg==';
  }
}

// 默认配置
export const defaultMapConfig: HuaweiMapConfig = {
  apiKey: getApiKey(),
  apiUrl: 'https://mapapi.cloud.huawei.com/mapjs/v1/api/js',
  defaultCenter: { lat: 23.130415047434678, lng: 113.32380294799805 }, // 广州
  defaultZoom: 8,
  defaultLanguage: 'zh',
  defaultSourceType: 'raster'
};

// 地图样式预设
export const mapStyles = {
  standard: 'standard',
  night: 'night',
  simple: 'simple'
} as const;

// 地图类型
export const mapTypes = {
  ROADMAP: 'ROADMAP',
  TERRAIN: 'TERRAIN'
} as const;

// Logo位置选项
export const logoPositions = {
  BOTTOM_LEFT: 'BOTTOM_LEFT',
  BOTTOM_RIGHT: 'BOTTOM_RIGHT',
  TOP_LEFT: 'TOP_LEFT',
  TOP_RIGHT: 'TOP_RIGHT'
} as const;

// 标记动画类型
export const markerAnimations = {
  DROP: 'DROP',
  BOUNCE: 'BOUNCE'
} as const;

// 常用城市坐标
export const commonCities = {
  beijing: { lat: 39.9042, lng: 116.4074 },
  shanghai: { lat: 31.2304, lng: 121.4737 },
  guangzhou: { lat: 23.1291, lng: 113.2644 },
  shenzhen: { lat: 22.5431, lng: 114.0579 },
  hangzhou: { lat: 30.2741, lng: 120.1551 },
  nanjing: { lat: 32.0603, lng: 118.7969 }
} as const;
